import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { createErrorResponse } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Debug endpoint for post scheduling issues
export async function POST(request: NextRequest) {
  console.log('🔍 Post Scheduling Debug - Started');
  
  try {
    // Use service role client to bypass RLS and access all data
    const supabase = createServiceRoleClient();
    
    const debugResults = {
      timestamp: new Date().toISOString(),
      phase: 'POST_SCHEDULING_DEBUG',
      tests: []
    };
    
    // Test 1: Check recent posts in posts table
    console.log('📋 Test 1: Check Recent Posts');
    
    const { data: recentPosts, error: postsError } = await supabase
      .from('posts')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (postsError) {
      console.error('❌ Error fetching recent posts:', postsError);
      return createErrorResponse('فشل في جلب المنشورات الحديثة', 500, postsError);
    }
    
    debugResults.tests.push({
      testName: 'Recent Posts Check',
      success: true,
      posts: recentPosts || [],
      count: recentPosts?.length || 0
    });
    
    console.log(`📊 Found ${recentPosts?.length || 0} recent posts`);
    
    // Test 2: Check scheduled_posts_queue table
    console.log('📅 Test 2: Check Scheduled Posts Queue');
    
    const { data: scheduledPosts, error: scheduledError } = await supabase
      .from('scheduled_posts_queue')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (scheduledError) {
      console.error('❌ Error fetching scheduled posts:', scheduledError);
      return createErrorResponse('فشل في جلب المنشورات المجدولة', 500, scheduledError);
    }
    
    debugResults.tests.push({
      testName: 'Scheduled Posts Queue Check',
      success: true,
      scheduled_posts: scheduledPosts || [],
      count: scheduledPosts?.length || 0
    });
    
    console.log(`📊 Found ${scheduledPosts?.length || 0} scheduled posts in queue`);
    
    // Test 3: Check post_social_accounts relationships
    console.log('🔗 Test 3: Check Post Social Accounts Relationships');
    
    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .select(`
        *,
        posts(id, content, status, created_at),
        social_accounts(id, platform, account_name, connection_status)
      `)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (psaError) {
      console.error('❌ Error fetching post social accounts:', psaError);
      return createErrorResponse('فشل في جلب علاقات المنشورات والحسابات', 500, psaError);
    }
    
    debugResults.tests.push({
      testName: 'Post Social Accounts Relationships',
      success: true,
      relationships: postSocialAccounts || [],
      count: postSocialAccounts?.length || 0
    });
    
    console.log(`📊 Found ${postSocialAccounts?.length || 0} post-social account relationships`);
    
    // Test 4: Test creating a new scheduled post
    console.log('🧪 Test 4: Test Creating New Scheduled Post');
    
    try {
      // Get a social account to use for testing
      const { data: socialAccounts, error: accountError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('platform', 'INSTAGRAM')
        .limit(1);
      
      if (accountError || !socialAccounts || socialAccounts.length === 0) {
        debugResults.tests.push({
          testName: 'Create New Scheduled Post',
          success: false,
          error: 'No Instagram accounts available for testing'
        });
      } else {
        const testAccount = socialAccounts[0];
        
        // Create a test post
        const testPostData = {
          content: '🧪 اختبار إنشاء منشور مجدول جديد\n\nهذا اختبار لنظام الجدولة\n\n#test #scheduling',
          media_urls: ['https://images.unsplash.com/photo-*************-80b023f02d71?w=800&h=800&fit=crop'],
          user_id: testAccount.user_id,
          status: 'DRAFT',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        const { data: newPost, error: postCreateError } = await supabase
          .from('posts')
          .insert(testPostData)
          .select()
          .single();
        
        if (postCreateError) {
          debugResults.tests.push({
            testName: 'Create New Scheduled Post',
            success: false,
            error: `Failed to create post: ${postCreateError.message}`
          });
        } else {
          // Create post-social account relationship
          const { data: newPSA, error: psaCreateError } = await supabase
            .from('post_social_accounts')
            .insert({
              post_id: newPost.id,
              social_account_id: testAccount.id,
              platform: testAccount.platform,
              status: 'pending',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();
          
          if (psaCreateError) {
            debugResults.tests.push({
              testName: 'Create New Scheduled Post',
              success: false,
              error: `Failed to create post-social account relationship: ${psaCreateError.message}`
            });
          } else {
            // Create scheduled post queue entry
            const scheduledFor = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now
            
            const { data: newScheduled, error: scheduleCreateError } = await supabase
              .from('scheduled_posts_queue')
              .insert({
                post_id: newPost.id,
                scheduled_for: scheduledFor.toISOString(),
                timezone: 'Asia/Riyadh',
                status: 'pending',
                priority: 'NORMAL',
                retry_count: 0,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select()
              .single();
            
            if (scheduleCreateError) {
              debugResults.tests.push({
                testName: 'Create New Scheduled Post',
                success: false,
                error: `Failed to create scheduled post queue entry: ${scheduleCreateError.message}`
              });
            } else {
              debugResults.tests.push({
                testName: 'Create New Scheduled Post',
                success: true,
                created_post: newPost,
                created_psa: newPSA,
                created_scheduled: newScheduled,
                scheduled_for: scheduledFor.toISOString()
              });
              
              console.log(`✅ Successfully created test scheduled post: ${newPost.id}`);
              console.log(`   Scheduled for: ${scheduledFor.toISOString()}`);
            }
          }
        }
      }
    } catch (error) {
      debugResults.tests.push({
        testName: 'Create New Scheduled Post',
        success: false,
        error: error.message
      });
      console.error('❌ Test post creation failed:', error);
    }
    
    // Test 5: Check database constraints and triggers
    console.log('🔧 Test 5: Check Database Schema');
    
    try {
      // Check if tables exist and have proper structure
      const { data: tablesInfo, error: schemaError } = await supabase
        .rpc('get_table_info', { table_names: ['posts', 'scheduled_posts_queue', 'post_social_accounts', 'social_accounts'] });
      
      debugResults.tests.push({
        testName: 'Database Schema Check',
        success: !schemaError,
        schema_info: tablesInfo || null,
        error: schemaError?.message || null
      });
      
    } catch (error) {
      debugResults.tests.push({
        testName: 'Database Schema Check',
        success: false,
        error: 'Schema check function not available'
      });
    }
    
    // Analysis
    const analysis = {
      total_posts: recentPosts?.length || 0,
      total_scheduled: scheduledPosts?.length || 0,
      total_relationships: postSocialAccounts?.length || 0,
      recent_posts_with_scheduling: recentPosts?.filter(p => 
        scheduledPosts?.some(sp => sp.post_id === p.id)
      ).length || 0,
      test_post_created: debugResults.tests.find(t => t.testName === 'Create New Scheduled Post')?.success || false
    };
    
    const recommendations = [];
    
    if (analysis.total_posts > 0 && analysis.total_scheduled === 0) {
      recommendations.push('المنشورات موجودة لكن لا توجد منشورات مجدولة - مشكلة في آلية الجدولة');
    }
    
    if (analysis.total_relationships === 0) {
      recommendations.push('لا توجد علاقات بين المنشورات والحسابات الاجتماعية');
    }
    
    if (!analysis.test_post_created) {
      recommendations.push('فشل في إنشاء منشور اختباري - تحقق من صلاحيات قاعدة البيانات');
    }
    
    if (analysis.recent_posts_with_scheduling === 0 && analysis.total_posts > 0) {
      recommendations.push('المنشورات الحديثة لا تظهر في قائمة الانتظار - مشكلة في ربط البيانات');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('نظام الجدولة يعمل بشكل صحيح');
    }
    
    console.log('📊 Post Scheduling Debug Analysis Complete');
    console.log(`   Total Posts: ${analysis.total_posts}`);
    console.log(`   Total Scheduled: ${analysis.total_scheduled}`);
    console.log(`   Test Post Created: ${analysis.test_post_created}`);
    
    return NextResponse.json({
      success: true,
      message: `تم فحص نظام جدولة المنشورات`,
      results: debugResults,
      analysis,
      recommendations,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Post scheduling debug error:', error);
    return createErrorResponse('خطأ في فحص نظام الجدولة', 500, error);
  }
}
