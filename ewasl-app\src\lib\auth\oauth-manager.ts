// Using enhanced Postiz providers instead of old services
import { FacebookEnhancedProvider } from '@/lib/social/postiz-integration/providers/facebook-enhanced';
import { LinkedInEnhancedProvider } from '@/lib/social/postiz-integration/providers/linkedin-enhanced';
import { TwitterEnhancedProvider } from '@/lib/social/postiz-integration/providers/twitter-enhanced';
// TwitterOAuthService replaced by TwitterEnhancedProvider
import { InstagramGraphService } from '@/lib/social/services/instagram-graph-service';
import { createTokenManager, TokenData } from './token-manager';
import crypto from 'crypto';

export interface OAuthInitiationResult {
  authUrl: string;
  state: string;
  codeVerifier?: string;
  oauthToken?: string;
  oauthTokenSecret?: string;
}

export interface OAuthCallbackResult {
  success: boolean;
  error?: string;
  accountData?: {
    accountId: string;
    accountName: string;
    profileData?: any;
  };
}

export class OAuthManager {
  private tokenManager = createTokenManager(true);
  private instagramGraphService = new InstagramGraphService();

  // Store PKCE challenges temporarily (in production, use Redis)
  private pkceStore = new Map<string, { codeVerifier: string; codeChallenge: string }>();

  /**
   * Generate secure state parameter for CSRF protection
   */
  private generateState(userId: string, platform: string): string {
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(16).toString('hex');
    return `${userId}_${platform}_${timestamp}_${random}`;
  }

  /**
   * Validate state parameter
   */
  private validateState(state: string, userId: string, platform: string): boolean {
    const parts = state.split('_');
    if (parts.length !== 4) return false;

    const [stateUserId, statePlatform, timestamp] = parts;

    // Check user ID and platform match
    if (stateUserId !== userId || statePlatform !== platform.toUpperCase()) {
      return false;
    }

    // Check timestamp is not too old (30 minutes)
    const stateTime = parseInt(timestamp);
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes

    return (now - stateTime) <= maxAge;
  }

  /**
   * Initiate OAuth flow for any platform
   */
  async initiateOAuth(userId: string, platform: string): Promise<OAuthInitiationResult> {
    const state = this.generateState(userId, platform);
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

    // Use consistent callback URLs for all platforms
    const getCallbackUrl = (platform: string): string => {
      return `${baseUrl}/api/social/callback/${platform.toLowerCase()}`;
    };

    const callbackUrl = getCallbackUrl(platform);

    switch (platform.toUpperCase()) {
      case 'TWITTER':
        return await this.initiateTwitterOAuth(callbackUrl, state);

      case 'FACEBOOK':
        return await this.initiateFacebookOAuth(callbackUrl, state);

      case 'INSTAGRAM':
        return await this.initiateInstagramOAuth(callbackUrl, state);

      case 'LINKEDIN':
        return await this.initiateLinkedInOAuth(callbackUrl, state);

      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Twitter OAuth 2.0 with PKCE initiation
   */
  private async initiateTwitterOAuth(callbackUrl: string, state: string): Promise<OAuthInitiationResult> {
    try {
      console.log('Initiating Twitter OAuth 2.0 with PKCE using enhanced provider...');

      const twitterProvider = new TwitterEnhancedProvider();
      const authResult = await twitterProvider.generateAuthUrl();

      // Store PKCE challenge temporarily for callback
      this.pkceStore.set(authResult.state, {
        codeVerifier: authResult.codeVerifier,
        codeChallenge: '', // Not needed for storage
      });

      console.log('Twitter OAuth 2.0 initiation successful');

      return {
        authUrl: authResult.url,
        state: authResult.state,
        codeVerifier: authResult.codeVerifier,
      };
    } catch (error: any) {
      console.error('Twitter OAuth initiation failed:', error);
      throw new Error(`Twitter OAuth initiation failed: ${error.message}`);
    }
  }

  /**
   * Facebook OAuth 2.0 initiation
   */
  private async initiateFacebookOAuth(callbackUrl: string, state: string): Promise<OAuthInitiationResult> {
    try {
      const facebookProvider = new FacebookEnhancedProvider();
      const authResult = await facebookProvider.generateAuthUrl();

      return {
        authUrl: authResult.url,
        state: authResult.state,
        codeVerifier: authResult.codeVerifier,
      };
    } catch (error: any) {
      throw new Error(`Facebook OAuth initiation failed: ${error.message}`);
    }
  }

  /**
   * Instagram OAuth 2.0 initiation (uses Facebook OAuth with Graph API scopes)
   */
  private async initiateInstagramOAuth(callbackUrl: string, state: string): Promise<OAuthInitiationResult> {
    try {
      console.log('Initiating Instagram Graph API OAuth...');

      const clientId = process.env.FACEBOOK_APP_ID;
      if (!clientId) {
        throw new Error('Facebook App ID not configured');
      }

      // Updated scopes for Instagram Graph API
      // Fixed: Removed invalid scopes instagram_graph_user_profile, instagram_graph_user_media
      const scopes = [
        'instagram_basic',
        'instagram_content_publish',
        'pages_show_list',
        'pages_read_engagement',
        'business_management'
      ].join(',');

      const params = new URLSearchParams({
        client_id: clientId,
        redirect_uri: callbackUrl,
        scope: scopes,
        response_type: 'code',
        state: `${state}_instagram`,
      });

      const authUrl = `https://www.facebook.com/v19.0/dialog/oauth?${params.toString()}`;

      console.log('Instagram Graph API OAuth initiation successful');

      return {
        authUrl,
        state: `${state}_instagram`,
      };
    } catch (error: any) {
      console.error('Instagram OAuth initiation failed:', error);
      throw new Error(`Instagram OAuth initiation failed: ${error.message}`);
    }
  }

  /**
   * LinkedIn OAuth 2.0 initiation
   */
  private async initiateLinkedInOAuth(callbackUrl: string, state: string): Promise<OAuthInitiationResult> {
    try {
      const linkedinProvider = new LinkedInEnhancedProvider();
      const authResult = await linkedinProvider.generateAuthUrl();

      return {
        authUrl: authResult.url,
        state: authResult.state,
        codeVerifier: authResult.codeVerifier,
      };
    } catch (error: any) {
      throw new Error(`LinkedIn OAuth initiation failed: ${error.message}`);
    }
  }

  /**
   * Handle OAuth callback for any platform
   */
  async handleCallback(
    platform: string,
    params: URLSearchParams,
    userId: string
  ): Promise<OAuthCallbackResult> {
    const state = params.get('state');

    if (!state || !this.validateState(state, userId, platform)) {
      return {
        success: false,
        error: 'Invalid or expired state parameter',
      };
    }

    switch (platform.toUpperCase()) {
      case 'TWITTER':
        return await this.handleTwitterCallback(params, userId);

      case 'FACEBOOK':
        return await this.handleFacebookCallback(params, userId);

      case 'INSTAGRAM':
        return await this.handleInstagramCallback(params, userId);

      case 'LINKEDIN':
        return await this.handleLinkedInCallback(params, userId);

      default:
        return {
          success: false,
          error: `Unsupported platform: ${platform}`,
        };
    }
  }

  /**
   * Handle Twitter OAuth 2.0 callback
   */
  private async handleTwitterCallback(params: URLSearchParams, userId: string): Promise<OAuthCallbackResult> {
    try {
      console.log('Handling Twitter OAuth 2.0 callback...');

      const code = params.get('code');
      const state = params.get('state');
      const error = params.get('error');

      if (error) {
        return { success: false, error: `Twitter OAuth error: ${error}` };
      }

      if (!code || !state) {
        return { success: false, error: 'Missing authorization code or state' };
      }

      // Get stored PKCE challenge
      const pkce = this.pkceStore.get(state);
      if (!pkce) {
        return { success: false, error: 'PKCE challenge not found or expired' };
      }

      // Clean up stored PKCE
      this.pkceStore.delete(state);

      const clientId = process.env.X_CLIENT_ID || process.env.TWITTER_CLIENT_ID;
      if (!clientId) {
        return { success: false, error: 'Twitter/X Client ID not configured' };
      }

      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
      const redirectUri = `${baseUrl}/api/social/callback/twitter`;

      // Exchange code for access token using enhanced provider
      const twitterProvider = new TwitterEnhancedProvider();
      const tokenResult = await twitterProvider.authenticate({
        code,
        codeVerifier: pkce.codeVerifier,
      });

      if (!tokenResult.accessToken) {
        return { success: false, error: 'Failed to get access token from Twitter' };
      }

      // Store tokens
      const tokenData: TokenData = {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken,
        tokenType: 'Bearer',
        expiresAt: tokenResult.expiresIn ? new Date(Date.now() + tokenResult.expiresIn * 1000) : undefined,
      };

      const storeResult = await this.tokenManager.storeTokens(
        userId,
        'TWITTER',
        tokenResult.id,
        tokenResult.username,
        tokenData,
        {
          id: tokenResult.id,
          username: tokenResult.username,
          name: tokenResult.name,
          picture: tokenResult.picture,
        }
      );

      if (!storeResult.success) {
        return { success: false, error: storeResult.error };
      }

      console.log('Twitter OAuth 2.0 callback handled successfully:', tokenResult.username);

      return {
        success: true,
        accountData: {
          accountId: tokenResult.id,
          accountName: tokenResult.username,
          profileData: {
            id: tokenResult.id,
            username: tokenResult.username,
            name: tokenResult.name,
            picture: tokenResult.picture,
          },
        },
      };
    } catch (error: any) {
      console.error('Twitter OAuth callback error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle Facebook OAuth callback
   */
  private async handleFacebookCallback(params: URLSearchParams, userId: string): Promise<OAuthCallbackResult> {
    try {
      const code = params.get('code');

      if (!code) {
        return { success: false, error: 'Missing authorization code' };
      }

      const facebookProvider = new FacebookEnhancedProvider();

      // Exchange code for access token
      const tokenResult = await facebookProvider.authenticate({
        code,
        codeVerifier: '', // Facebook doesn't use PKCE
      });

      if (!tokenResult.accessToken) {
        return { success: false, error: 'Failed to get access token' };
      }

      // Store tokens
      const tokenData: TokenData = {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken,
        tokenType: 'Bearer',
        expiresAt: tokenResult.expiresIn ? new Date(Date.now() + tokenResult.expiresIn * 1000) : undefined,
      };

      const storeResult = await this.tokenManager.storeTokens(
        userId,
        'FACEBOOK',
        tokenResult.id,
        tokenResult.name,
        tokenData,
        {
          id: tokenResult.id,
          name: tokenResult.name,
          username: tokenResult.username,
          picture: tokenResult.picture,
        }
      );

      if (!storeResult.success) {
        return { success: false, error: storeResult.error };
      }

      return {
        success: true,
        accountData: {
          accountId: tokenResult.id,
          accountName: tokenResult.name,
          profileData: {
            id: tokenResult.id,
            name: tokenResult.name,
            username: tokenResult.username,
            picture: tokenResult.picture,
          },
        },
      };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle Instagram Graph API OAuth callback
   */
  private async handleInstagramCallback(params: URLSearchParams, userId: string): Promise<OAuthCallbackResult> {
    try {
      console.log('Handling Instagram Graph API OAuth callback...');

      const code = params.get('code');
      const error = params.get('error');

      if (error) {
        return { success: false, error: `Instagram OAuth error: ${error}` };
      }

      if (!code) {
        return { success: false, error: 'Missing authorization code' };
      }

      // Instagram uses Facebook OAuth, so we use Facebook enhanced provider
      const facebookProvider = new FacebookEnhancedProvider();

      // Exchange code for access token
      const tokenResult = await facebookProvider.authenticate({
        code,
        codeVerifier: '', // Facebook doesn't use PKCE
      });

      // Use Instagram Graph Service to get business accounts
      const instagramAccounts = await this.instagramGraphService.getBusinessAccounts(tokenResult.accessToken);

      if (instagramAccounts.length === 0) {
        return {
          success: false,
          error: 'No Instagram business accounts found. Please connect an Instagram business account to your Facebook page and ensure it has the required permissions.'
        };
      }

      // Use the first Instagram account (in production, let user choose)
      const instagramAccount = instagramAccounts[0];

      // Test the connection to ensure it's working
      try {
        await this.instagramGraphService.testConnection(instagramAccount);
      } catch (testError) {
        console.warn('Instagram connection test failed:', testError);
        // Continue anyway, as the test might fail due to permissions but basic connection might work
      }

      // Store tokens with enhanced data
      const tokenData: TokenData = {
        accessToken: tokenResult.accessToken,
        tokenType: (tokenResult as any).tokenType || 'Bearer',
        expiresAt: tokenResult.expiresIn ? new Date(Date.now() + tokenResult.expiresIn * 1000) : undefined,
      };

      const profileData = {
        id: instagramAccount.id,
        username: instagramAccount.username,
        pageId: instagramAccount.pageId,
        pageName: instagramAccount.pageName,
        profilePictureUrl: instagramAccount.profilePictureUrl,
        followersCount: instagramAccount.followersCount,
        mediaCount: instagramAccount.mediaCount,
      };

      const storeResult = await this.tokenManager.storeTokens(
        userId,
        'INSTAGRAM',
        instagramAccount.id,
        `@${instagramAccount.username}`,
        tokenData,
        profileData
      );

      if (!storeResult.success) {
        return { success: false, error: storeResult.error };
      }

      console.log('Instagram Graph API OAuth callback handled successfully:', instagramAccount.username);

      return {
        success: true,
        accountData: {
          accountId: instagramAccount.id,
          accountName: `@${instagramAccount.username}`,
          profileData,
        },
      };
    } catch (error: any) {
      console.error('Instagram OAuth callback error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle LinkedIn OAuth callback
   */
  private async handleLinkedInCallback(params: URLSearchParams, userId: string): Promise<OAuthCallbackResult> {
    try {
      const code = params.get('code');

      if (!code) {
        return { success: false, error: 'Missing authorization code' };
      }

      const linkedinProvider = new LinkedInEnhancedProvider();

      // Exchange code for access token
      const tokenResult = await linkedinProvider.authenticate({
        code,
        codeVerifier: '', // LinkedIn doesn't use PKCE in this implementation
      });

      if (!tokenResult.accessToken) {
        return { success: false, error: 'Failed to get access token' };
      }

      // Store tokens
      const tokenData: TokenData = {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken,
        tokenType: 'Bearer',
        expiresAt: tokenResult.expiresIn ? new Date(Date.now() + tokenResult.expiresIn * 1000) : undefined,
      };

      const storeResult = await this.tokenManager.storeTokens(
        userId,
        'LINKEDIN',
        tokenResult.id,
        tokenResult.name,
        tokenData,
        {
          id: tokenResult.id,
          name: tokenResult.name,
          username: tokenResult.username,
          picture: tokenResult.picture,
        }
      );

      if (!storeResult.success) {
        return { success: false, error: storeResult.error };
      }

      return {
        success: true,
        accountData: {
          accountId: tokenResult.id,
          accountName: tokenResult.name,
          profileData: {
            id: tokenResult.id,
            name: tokenResult.name,
            username: tokenResult.username,
            picture: tokenResult.picture,
          },
        },
      };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }
}

// Helper function to create OAuthManager instance
export function createOAuthManager(): OAuthManager {
  return new OAuthManager();
}
