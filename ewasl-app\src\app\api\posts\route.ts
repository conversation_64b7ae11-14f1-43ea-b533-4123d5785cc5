import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import SocialMediaError<PERSON>and<PERSON> from '@/lib/social/error-handler';
import { getAuthenticatedUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth/api-auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// DEPLOYMENT TRIGGER: Force new deployment with enhanced error handling - 2024-12-19 - ATTEMPT 2

// Validation schema for creating posts - Updated to handle both platforms and social_account_ids
const createPostSchema = z.object({
  content: z.string().min(1, 'المحتوى مطلوب').max(2800, 'المحتوى طويل جداً'),
  media_urls: z.array(z.string().url()).default([]),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']).default('DRAFT'),
  scheduled_at: z.union([z.string().datetime(), z.undefined(), z.null()]).optional(),
  scheduled_for: z.union([z.string().datetime(), z.undefined(), z.null()]).optional(), // Add scheduled_for support
  // Support both formats for backward compatibility
  platforms: z.array(z.string()).optional(),
  social_account_ids: z.array(z.string()).optional(),
  timezone: z.string().optional().default('UTC'),
  priority: z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'), // Add priority support
}).refine(data => {
  // Ensure at least one of platforms or social_account_ids is provided and not empty
  return (data.platforms && data.platforms.length > 0) ||
         (data.social_account_ids && data.social_account_ids.length > 0);
}, {
  message: 'يرجى اختيار منصة واحدة على الأقل',
  path: ['platforms']
});

// GET - Fetch user's posts
export async function GET(request: NextRequest) {
  try {
    console.log('📝 Fetching posts with unified auth...');
    
    // Use unified authentication
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Fetching posts for user:', user.id);

    // Build query
    let query = supabase
      .from('posts')
      .select(`
        id,
        content,
        media_urls,
        status,
        scheduled_at,
        published_at,
        timezone,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status && status !== 'ALL') {
      query = query.eq('status', status);
    }

    const { data: posts, error } = await query;

    if (error) {
      console.error('Error fetching posts:', error);
      return createErrorResponse('Failed to fetch posts', 500, error);
    }

    console.log(`✅ Fetched ${posts?.length || 0} posts`);

    return createAuthenticatedResponse({
      posts: posts || [],
      total: posts?.length || 0,
      user: {
        id: user.id,
        email: user.email
      }
    });

  } catch (error: any) {
    console.error('❌ Posts fetch error:', error);
    if (error.message.includes('Authentication')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    return createErrorResponse('Internal server error', 500, error.message);
  }
}

// POST - Create new post
export async function POST(request: NextRequest) {
  console.log('🚀 POST /api/posts - Handler started');
  console.log('🌍 Environment check:', {
    hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    nodeEnv: process.env.NODE_ENV
  });

  try {
    console.log('📝 Creating new post with unified auth...');

    // Use unified authentication
    console.log('🔐 Attempting authentication...');
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    // Check if publishing_results table exists (do this once at the beginning)
    console.log('📊 Checking if publishing_results table exists...');
    const { data: tableCheck, error: tableCheckError } = await supabase
      .from('publishing_results')
      .select('id')
      .limit(1);

    const publishingTableExists = !tableCheckError;
    console.log('📊 Publishing results table exists:', publishingTableExists);

    // Also check if scheduled_posts_queue table exists
    console.log('📊 Checking if scheduled_posts_queue table exists...');
    const { data: queueCheck, error: queueCheckError } = await supabase
      .from('scheduled_posts_queue')
      .select('id')
      .limit(1);

    const queueTableExists = !queueCheckError;
    console.log('📊 Scheduled posts queue table exists:', queueTableExists);

    // Get request body
    const body = await request.json();
    console.log('Request body received:', { ...body, content: body.content?.substring(0, 50) + '...' });

    // Validate request body
    const validation = createPostSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return createErrorResponse(
        'بيانات غير صحيحة',
        400,
        validation.error.errors.map(err => err.message).join(', ')
      );
    }

    // Extract data from validation result
    const { content, media_urls, timezone, priority } = validation.data;

    // Handle both scheduled_at and scheduled_for parameters (for backward compatibility)
    const scheduled_for = validation.data.scheduled_for || validation.data.scheduled_at;

    // Determine post status based on scheduling
    let status = validation.data.status;
    if (scheduled_for && (status === 'DRAFT' || !status)) {
      status = 'SCHEDULED'; // Auto-set status to SCHEDULED when scheduled_for is provided
      console.log('📅 Auto-setting status to SCHEDULED due to scheduled_for parameter');
    } else if (!status) {
      status = 'DRAFT'; // Default to DRAFT if no status specified
    }

    console.log('📊 Final status determined:', status);

    // Handle both platforms and social_account_ids (for backward compatibility)
    const platforms = validation.data.platforms || [];
    const social_account_ids = validation.data.social_account_ids || [];

    console.log('Creating post for user:', user.id);
    console.log('Selected platforms:', platforms);
    console.log('Selected social_account_ids:', social_account_ids);
    console.log('Payload received:', { content, media_urls, status, scheduled_for, platforms, social_account_ids, timezone });

    // Get social accounts based on the provided data
    let socialAccountsQuery = supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    // If platforms are provided, filter by platform
    if (platforms.length > 0) {
      console.log('Querying social accounts by platform:', platforms.map(p => p.toUpperCase()));
      socialAccountsQuery = socialAccountsQuery.in('platform', platforms.map(p => p.toUpperCase()));
    }
    // If social_account_ids are provided, filter by account IDs
    else if (social_account_ids.length > 0) {
      console.log('Querying social accounts by IDs:', social_account_ids);
      socialAccountsQuery = socialAccountsQuery.in('id', social_account_ids);
    }

    const { data: socialAccounts, error: accountsError } = await socialAccountsQuery;

    console.log('🔍 CRITICAL DEBUG - Social accounts query result:', {
      socialAccounts,
      accountsError,
      queryUserId: user.id,
      socialAccountIds: social_account_ids,
      platforms: platforms
    });

    if (accountsError) {
      console.error('❌ Error fetching social accounts:', accountsError);
      return createErrorResponse('فشل في جلب الحسابات الاجتماعية', 500, accountsError);
    }

    // CRITICAL DEBUG: Check if social accounts were found
    if (!socialAccounts || socialAccounts.length === 0) {
      console.error('❌ CRITICAL: No social accounts found for user:', user.id);
      console.log('🔍 DEBUG: Checking if accounts exist for different user...');

      // Debug: Check if the accounts exist but belong to different user
      if (social_account_ids.length > 0) {
        const { data: debugAccounts } = await supabase
          .from('social_accounts')
          .select('id, user_id, platform, account_name')
          .in('id', social_account_ids);

        console.log('🔍 DEBUG: Accounts found (any user):', debugAccounts);
      }

      return createErrorResponse(
        'لا توجد حسابات اجتماعية متصلة للمنصات المحددة',
        400,
        `No social accounts found for user ${user.id}`
      );
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return createErrorResponse(
        'لا توجد حسابات اجتماعية متصلة للمنصات المحددة',
        400,
        'No connected social accounts found for selected platforms'
      );
    }

    console.log(`Found ${socialAccounts.length} connected social accounts`);

    // Create post
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_urls, // Use the array directly
        status,
        scheduled_at: scheduled_for ? new Date(scheduled_for).toISOString() : null,
        timezone,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating post:', createError);
      return createErrorResponse('فشل في إنشاء المنشور', 500, createError);
    }

    console.log('✅ Post created successfully:', post.id);

    // Create publishing_results entries for tracking (with fallback for missing table)

    if (publishingTableExists) {
      // Create publishing_results entries
      const publishingResultsData = socialAccounts.map(account => ({
        post_id: post.id,
        social_account_id: account.id,
        platform: account.platform,
        success: false, // Will be updated when publishing completes
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: relationshipError } = await supabase
        .from('publishing_results')
        .insert(publishingResultsData);

      if (relationshipError) {
        console.error('Error creating publishing_results entries:', relationshipError);
        console.warn('⚠️ Failed to create publishing_results entries, continuing...');
      } else {
        console.log(`✅ Created ${publishingResultsData.length} publishing_results entries`);
      }
    } else {
      console.log('⚠️ Publishing results table not found, skipping relationship creation...');
      console.log('💡 Post will still be created and published, but results won\'t be tracked in separate table');
    }

    // CRITICAL FIX: Create post_social_accounts entries (MISSING IN ORIGINAL CODE)
    console.log('🔗 Creating post_social_accounts entries...');

    const postSocialAccountsData = socialAccounts.map(account => ({
      post_id: post.id,
      social_account_id: account.id,
      platform: account.platform,
      status: status === 'PUBLISHED' ? 'pending' : 'pending', // Use lowercase 'pending' for both DRAFT and PUBLISHED initially
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    console.log('🔍 CRITICAL DEBUG - post_social_accounts insertion data:', {
      postSocialAccountsData,
      socialAccountsCount: socialAccounts.length,
      socialAccountsPlatforms: socialAccounts.map(a => ({ id: a.id, platform: a.platform }))
    });

    // Validate that all platforms are not null
    const nullPlatforms = postSocialAccountsData.filter(data => !data.platform);
    if (nullPlatforms.length > 0) {
      console.error('❌ CRITICAL: Found null platforms in insertion data:', nullPlatforms);
      return createErrorResponse(
        'خطأ في بيانات المنصات الاجتماعية',
        500,
        'Some social accounts have null platform values'
      );
    }

    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .insert(postSocialAccountsData)
      .select();

    if (psaError) {
      console.error('❌ CRITICAL: Failed to create post_social_accounts entries:', psaError);

      // Clean up: Delete the created post since we can't link it to social accounts
      await supabase.from('posts').delete().eq('id', post.id);

      return createErrorResponse('فشل في ربط المنشور بالحسابات الاجتماعية', 500, psaError);
    }

    console.log(`✅ Created ${postSocialAccounts.length} post_social_accounts entries`);

    // TODO: Add usage tracking for billing purposes in future payment system implementation

    // Initialize publishResults array for response (declare outside the if block to avoid scope issues)
    let publishResults: any[] = [];

    // If status is PUBLISHED, publish immediately
    if (status === 'PUBLISHED') {
      console.log('🚀 Immediate publishing requested for post:', post.id);

      try {
        console.log('🔍 DEBUGGING - Importing enhanced publishing functions...');

        // Import enhanced social media publishing functions with better error handling
        const { publishToFacebookEnhanced, publishToInstagramEnhanced } = await import('@/lib/social/enhanced-publishing');
        console.log('✅ Enhanced publishing functions imported successfully');

        // Publish to each selected account
        for (const account of socialAccounts) {
          console.log(`📤 Publishing to ${account.platform} (${account.account_name})`);
          console.log('Account details:', {
            id: account.id,
            platform: account.platform,
            account_id: account.account_id,
            account_name: account.account_name,
            hasAccessToken: !!account.access_token,
            hasPageId: !!account.page_id,
            hasPageToken: !!account.page_access_token,
            hasInstagramBusinessId: !!account.instagram_business_account_id,
            metadata: account.metadata
          });

          let result;

          // Route to appropriate publisher based on platform
          try {
            console.log(`🔍 DEBUGGING - Starting ${account.platform} publishing...`);

            switch (account.platform.toUpperCase()) {
              case 'FACEBOOK':
                console.log('📘 Calling publishToFacebookEnhanced...');
                result = await publishToFacebookEnhanced(content, media_urls, account, {
                  timezone,
                  postId: post.id
                });
                break;
              case 'INSTAGRAM':
                console.log('📸 ALTERNATIVE APPROACH - Using exact working endpoint pattern...');

                // WORKING PATTERN: Copy exact logic from /api/posts/publish endpoint
                const instagramAccountId = account.metadata?.instagramAccountId || account.account_id;

                console.log('📸 WORKING PATTERN - Instagram publishing with direct implementation:', {
                  instagramAccountId,
                  has_access_token: !!account.access_token,
                  access_token_length: account.access_token?.length,
                  media_urls_count: media_urls?.length
                });

                // WORKING PATTERN: Direct Instagram publishing implementation (copied from working endpoint)
                try {
                  if (!media_urls || media_urls.length === 0) {
                    result = {
                      success: false,
                      error: 'منشورات انستغرام تتطلب صورة أو فيديو واحد على الأقل',
                      details: 'Instagram posts require at least one image or video',
                      step: 'media_validation',
                      platform: 'INSTAGRAM'
                    };
                  } else {
                    const mediaUrl = media_urls[0];
                    const caption = content.replace(/<[^>]*>/g, ''); // Strip HTML tags

                    // Step 1: Create media container with appsecret_proof
                    const crypto = require('crypto');
                    const appSecret = process.env.FACEBOOK_APP_SECRET || 'your_app_secret_here';
                    const appsecret_proof = crypto.createHmac('sha256', appSecret).update(account.access_token).digest('hex');

                    const containerData = {
                      image_url: mediaUrl,
                      caption: caption,
                      access_token: account.access_token,
                      appsecret_proof: appsecret_proof
                    };

                    console.log('📸 WORKING PATTERN - Creating Instagram media container...');
                    const containerResponse = await fetch(
                      `https://graph.facebook.com/v18.0/${instagramAccountId}/media`,
                      {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(containerData),
                      }
                    );

                    const containerResult = await containerResponse.json();

                    if (!containerResponse.ok) {
                      result = {
                        success: false,
                        error: `فشل في إنشاء حاوية الوسائط لانستغرام: ${containerResult.error?.message || 'خطأ غير معروف'}`,
                        details: containerResult,
                        step: 'container_creation',
                        platform: 'INSTAGRAM'
                      };
                    } else {
                      const creationId = containerResult.id;
                      console.log('✅ WORKING PATTERN - Instagram media container created:', creationId);

                      // Step 2: Publish the media with appsecret_proof
                      const publishData = {
                        creation_id: creationId,
                        access_token: account.access_token,
                        appsecret_proof: appsecret_proof
                      };

                      console.log('📸 WORKING PATTERN - Publishing Instagram media container...');
                      const publishResponse = await fetch(
                        `https://graph.facebook.com/v18.0/${instagramAccountId}/media_publish`,
                        {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify(publishData),
                        }
                      );

                      const publishResult = await publishResponse.json();

                      if (!publishResponse.ok) {
                        result = {
                          success: false,
                          error: `فشل في النشر على انستغرام: ${publishResult.error?.message || 'خطأ غير معروف'}`,
                          details: publishResult,
                          step: 'publishing',
                          platform: 'INSTAGRAM'
                        };
                      } else {
                        console.log('🎉 WORKING PATTERN - Instagram post published successfully:', publishResult);
                        result = {
                          success: true,
                          postId: publishResult.id,
                          postUrl: `https://instagram.com/p/${publishResult.id}`,
                          platform: 'INSTAGRAM',
                          step: 'completed'
                        };
                      }
                    }
                  }
                } catch (instagramError: any) {
                  console.error('❌ WORKING PATTERN - Instagram publishing error:', instagramError);
                  result = {
                    success: false,
                    error: `خطأ في النشر على انستغرام: ${instagramError.message}`,
                    details: instagramError,
                    step: 'critical_error',
                    platform: 'INSTAGRAM'
                  };
                }

                console.log('📸 FIXED - Instagram publishing result:', {
                  success: result?.success,
                  error: result?.error,
                  step: result?.step
                });
                break;
              default:
                result = {
                  success: false,
                  error: `Platform ${account.platform} not yet supported`,
                  step: 'platform_validation',
                  platform: account.platform
                };
            }

            console.log(`🔍 DEBUGGING - ${account.platform} publishing result:`, {
              success: result.success,
              error: result.error,
              step: result.step,
              platform: result.platform
            });

          } catch (publishError: any) {
            console.error(`❌ CRITICAL ERROR in ${account.platform} publishing:`, publishError);
            result = {
              success: false,
              error: `Publishing error: ${publishError.message}`,
              step: 'publishing_exception',
              platform: account.platform,
              details: {
                message: publishError.message,
                stack: publishError.stack
              }
            };
          }

          publishResults.push({ ...result, platform: account.platform, accountId: account.id });

          console.log(`${result.success ? '✅' : '❌'} ${account.platform} publish result:`, {
            success: result.success,
            postId: result.postId,
            error: result.error
          });

          // CRITICAL FIX: Update post_social_accounts entry with publishing result
          const accountIndex = socialAccounts.findIndex(acc => acc.id === account.id);
          if (accountIndex !== -1 && postSocialAccounts[accountIndex]) {
            const psaEntry = postSocialAccounts[accountIndex];

            const updateData: any = {
              status: result.success ? 'published' : 'failed',
              updated_at: new Date().toISOString()
            };

            if (result.success) {
              updateData.platform_post_id = result.postId;
              updateData.platform_url = result.url;
              updateData.published_at = new Date().toISOString();
            } else {
              updateData.error_message = result.error;
            }

            // Update the specific post_social_accounts entry
            const { error: updateError } = await supabase
              .from('post_social_accounts')
              .update(updateData)
              .eq('id', psaEntry.id);

            if (updateError) {
              console.error('❌ Failed to update post_social_accounts entry:', updateError);
            } else {
              console.log(`✅ Updated post_social_accounts entry for ${account.platform}`);
            }
          }

          // Update publishing_results with publishing result (if table exists)
          if (publishingTableExists) {
            try {
              await supabase
                .from('publishing_results')
                .update({
                  success: result.success,
                  external_post_id: result.postId || null,
                  external_url: result.url || null,
                  published_at: result.success ? new Date().toISOString() : null,
                  error_message: result.error || null,
                  updated_at: new Date().toISOString()
                })
                .eq('post_id', post.id)
                .eq('social_account_id', account.id);
            } catch (updateError) {
              console.warn('⚠️ Failed to update publishing_results:', updateError);
            }
          } else {
            console.log('⚠️ Skipping publishing_results update (table not found)');
          }
        }
        
        // Update post status based on results
        const allSuccessful = publishResults.every(r => r.success);
        const allFailed = publishResults.every(r => !r.success);
        const someSuccessful = publishResults.some(r => r.success);

        let finalStatus: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' = status;
        if (allFailed) {
          finalStatus = 'DRAFT'; // Keep as draft if all failed
        } else if (allSuccessful) {
          finalStatus = 'PUBLISHED';
        } else if (someSuccessful) {
          finalStatus = 'PUBLISHED'; // Consider partially successful as published
        }

        // Update post with final status and publish time
        await supabase
          .from('posts')
          .update({
            status: finalStatus,
            published_at: someSuccessful ? new Date().toISOString() : null,
            updated_at: new Date().toISOString()
          })
          .eq('id', post.id);

        // Update the post object returned to client
        post.status = finalStatus;
        post.published_at = someSuccessful ? new Date().toISOString() : null;

        console.log(`📊 Publishing summary: ${publishResults.filter(r => r.success).length}/${publishResults.length} successful`);
        
      } catch (publishError) {
        console.error('💥 Critical publishing error:', publishError);

        // Update post status to DRAFT (failed to publish)
        await supabase
          .from('posts')
          .update({
            status: 'DRAFT',
            updated_at: new Date().toISOString()
          })
          .eq('id', post.id);

        post.status = 'DRAFT';

        // CRITICAL FIX: Update all post_social_accounts to failed status
        await supabase
          .from('post_social_accounts')
          .update({
            status: 'failed',
            error_message: publishError.message,
            updated_at: new Date().toISOString()
          })
          .eq('post_id', post.id);

        // Update all publishing_results to failed status (if table exists)
        if (publishingTableExists) {
          await supabase
            .from('publishing_results')
            .update({
              success: false,
              error_message: publishError.message,
              updated_at: new Date().toISOString()
            })
            .eq('post_id', post.id);
        }
      }
    }

    // Add to scheduled posts queue if scheduled_for is provided and queue table exists
    if (scheduled_for && queueTableExists) {
      console.log('📅 Adding post to scheduled queue:', post.id);
      console.log('📅 Scheduled for:', scheduled_for);
      console.log('📅 Status:', status);

      try {
        const { error: queueError } = await supabase
          .from('scheduled_posts_queue')
          .insert({
            post_id: post.id,
            scheduled_for: new Date(scheduled_for).toISOString(),
            timezone: timezone || 'UTC',
            status: 'pending',
            priority: priority || 'NORMAL', // Use provided priority or default
            retry_count: 0, // Add default retry count
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (queueError) {
          console.error('❌ Error adding post to queue:', queueError);
          console.warn('⚠️ Failed to add post to scheduled queue, but post was created');
          console.error('Queue error details:', JSON.stringify(queueError, null, 2));
        } else {
          console.log('✅ Post added to scheduled queue successfully');
          console.log('✅ Queue entry created for post:', post.id);
        }
      } catch (queueError) {
        console.error('💥 Critical queue error:', queueError);
        console.warn('⚠️ Failed to add post to scheduled queue, but post was created');
        console.error('Critical error details:', JSON.stringify(queueError, null, 2));
      }
    } else if (scheduled_for && !queueTableExists) {
      console.warn('⚠️ Scheduled post requested but queue table does not exist');
      console.log('💡 Post created but cannot be scheduled - queue table missing');
    } else if (!scheduled_for) {
      console.log('📝 Post created without scheduling (immediate or draft)');
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        post_id: post.id,
        action: status === 'PUBLISHED' ? 'POST_PUBLISHED' : status === 'SCHEDULED' ? 'POST_SCHEDULED' : 'POST_CREATED',
        details: `Post ${status.toLowerCase()}: ${content.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    return createAuthenticatedResponse({
      post,
      message: status === 'PUBLISHED' ? 'تم نشر المنشور بنجاح' :
               status === 'SCHEDULED' ? 'تم جدولة المنشور بنجاح' :
               'تم حفظ المنشور كمسودة',
      post_social_accounts: postSocialAccounts,
      publishResults: status === 'PUBLISHED' ? publishResults : undefined,
    }, 201);

  } catch (error: any) {
    console.error('❌ CRITICAL ERROR in POST /api/posts:');
    console.error('❌ Error message:', error.message);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Error name:', error.name);
    console.error('❌ Error code:', error.code);
    console.error('❌ Full error object:', JSON.stringify(error, null, 2));

    // Enhanced error logging for debugging
    console.error('🔍 DEBUGGING INFO:');
    console.error('🔍 Request URL:', request.url);
    console.error('🔍 Request method:', request.method);
    console.error('🔍 Environment variables check:', {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      nodeEnv: process.env.NODE_ENV
    });

    // Log the specific step where error occurred
    if (error.step) {
      console.error('🔍 Error occurred at step:', error.step);
    }

    // Log Facebook API specific errors
    if (error.platform === 'FACEBOOK') {
      console.error('🔍 Facebook API Error Details:', {
        platform: error.platform,
        step: error.step,
        details: error.details
      });
    }

    if (error.message.includes('Authentication')) {
      return NextResponse.json(
        { error: 'المصادقة مطلوبة' },
        { status: 401 }
      );
    }

    // Return more detailed error information for debugging
    return createErrorResponse(
      'خطأ في الخادم الداخلي',
      500,
      process.env.NODE_ENV === 'development' ? {
        message: error.message,
        step: error.step,
        platform: error.platform,
        details: error.details
      } : error.message
    );
  }
}