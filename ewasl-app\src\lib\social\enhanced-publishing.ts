/**
 * Enhanced Publishing Functions with Better Error Handling
 * Addresses the 500 error issues in /api/posts
 */

import { ensureValidToken } from '@/lib/oauth-token-manager';

export interface EnhancedPublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
  details?: any;
  step?: string;
  platform?: string;
}

/**
 * Enhanced Facebook Publisher with detailed error handling
 */
export async function publishToFacebookEnhanced(
  content: string,
  mediaUrls?: string[],
  account?: any,
  metadata?: any
): Promise<EnhancedPublishResult> {
  try {
    console.log('📘 [ENHANCED] Publishing to Facebook:', {
      content: content.substring(0, 50) + '...',
      mediaCount: mediaUrls?.length || 0,
      accountId: account?.account_id,
      accountName: account?.account_name,
      hasPageId: !!account?.page_id,
      hasPageToken: !!account?.page_access_token
    });

    // Step 1: Validate account data
    if (!account) {
      return {
        success: false,
        error: 'حساب فيسبوك غير متوفر',
        details: 'No Facebook account provided to publisher',
        step: 'account_validation',
        platform: 'FACEBOOK'
      };
    }

    if (!account.access_token) {
      return {
        success: false,
        error: 'رمز الوصول لحساب فيسبوك غير متوفر',
        details: 'Facebook access token not found for user account',
        step: 'token_validation',
        platform: 'FACEBOOK'
      };
    }

    // Step 2: Ensure valid token (with automatic refresh if needed)
    console.log('🔑 Ensuring valid Facebook token...');
    const tokenResult = await ensureValidToken(account.account_id, account.access_token);

    if (!tokenResult.success) {
      console.error('❌ Facebook token validation/refresh failed:', tokenResult.error);
      return {
        success: false,
        error: tokenResult.error || 'انتهت صلاحية رمز الوصول لحساب فيسبوك. يرجى إعادة ربط الحساب.',
        details: { token_refresh_attempted: true, original_error: tokenResult.error },
        step: 'token_validation',
        platform: 'FACEBOOK'
      };
    }

    // Use the validated/refreshed token
    const validToken = tokenResult.token;
    if (tokenResult.refreshed) {
      console.log('🔄 Facebook token was refreshed automatically');
    }

    // Verify token works by getting user info
    const tokenResponse = await fetch(`https://graph.facebook.com/v19.0/me?access_token=${validToken}`);
    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      console.error('❌ Facebook token still invalid after refresh:', tokenData);
      return {
        success: false,
        error: 'رمز الوصول لا يزال غير صالح بعد المحاولة التلقائية للتحديث',
        details: tokenData,
        step: 'token_validation',
        platform: 'FACEBOOK'
      };
    }

    console.log('✅ Facebook token is valid for user:', tokenData.name);

    // Step 3: Determine publishing target
    let publishingToken = validToken; // Use the validated/refreshed token
    let targetId = 'me'; // Default to user's personal feed

    if (account.page_id && account.page_access_token) {
      publishingToken = account.page_access_token;
      targetId = account.page_id;
      console.log(`📘 Publishing to Facebook Page: ${account.page_name || account.page_id}`);

      // Validate page token
      const pageTokenResponse = await fetch(`https://graph.facebook.com/v19.0/${targetId}?access_token=${publishingToken}`);
      if (!pageTokenResponse.ok) {
        const pageTokenData = await pageTokenResponse.json();
        console.error('❌ Facebook page token validation failed:', pageTokenData);
        return {
          success: false,
          error: 'رمز الوصول لصفحة فيسبوك غير صالح',
          details: pageTokenData,
          step: 'page_token_validation',
          platform: 'FACEBOOK'
        };
      }
    } else {
      console.log('📘 Publishing to user feed (no page configured)');
    }

    // Step 4: Prepare post data
    const postData: any = {
      message: content,
      access_token: publishingToken,
    };

    // Handle media attachments (simplified for now)
    if (mediaUrls && mediaUrls.length > 0) {
      console.log('📎 Media attachments detected, using link format');
      postData.link = mediaUrls[0]; // Use first media as link for now
    }

    // Step 5: Publish the post
    const publishStartTime = Date.now();
    const timestamp = new Date().toISOString();

    console.log(`📤 [${timestamp}] Publishing to Facebook feed...`);
    console.log(`🔍 [${timestamp}] ENHANCED DEBUGGING - Final request details:`, {
      url: `https://graph.facebook.com/v19.0/${targetId}/feed`,
      method: 'POST',
      targetId: targetId,
      targetType: account.page_id ? 'PAGE' : 'USER',
      contentLength: content.length,
      hasMedia: !!(mediaUrls && mediaUrls.length > 0),
      tokenRefreshed: tokenResult.refreshed,
      postData: { ...postData, access_token: '[REDACTED]' }
    });

    // Facebook Graph API requires form-encoded data, not JSON
    const formData = new URLSearchParams();
    Object.keys(postData).forEach(key => {
      formData.append(key, postData[key]);
    });

    const formDataString = formData.toString().replace(/access_token=[^&]+/, 'access_token=[REDACTED]');
    console.log(`🔍 [${timestamp}] ENHANCED DEBUGGING - Form data prepared (${formDataString.length} chars):`, formDataString);

    console.log(`🚀 [${timestamp}] Sending Facebook Graph API request...`);
    const apiCallStartTime = Date.now();

    const response = await fetch(`https://graph.facebook.com/v19.0/${targetId}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'eWasl-Social-Scheduler/1.0'
      },
      body: formData,
    });

    const apiCallDuration = Date.now() - apiCallStartTime;
    const responseTimestamp = new Date().toISOString();

    console.log(`📡 [${responseTimestamp}] ENHANCED DEBUGGING - API Response received:`, {
      status: response.status,
      statusText: response.statusText,
      duration_ms: apiCallDuration,
      headers: Object.fromEntries(response.headers.entries())
    });

    let result;
    try {
      result = await response.json();
      console.log(`📋 [${responseTimestamp}] ENHANCED DEBUGGING - Response body parsed:`, {
        success: response.ok,
        hasId: !!(result.id),
        hasError: !!(result.error),
        errorType: result.error?.type,
        errorCode: result.error?.code,
        errorSubcode: result.error?.error_subcode,
        fullResponse: result
      });
    } catch (parseError) {
      console.error(`❌ [${responseTimestamp}] Failed to parse Facebook API response:`, parseError);
      return {
        success: false,
        error: 'فشل في تحليل استجابة فيسبوك',
        details: { parseError: parseError instanceof Error ? parseError.message : 'خطأ غير معروف' },
        step: 'response_parsing',
        platform: 'FACEBOOK'
      };
    }

    if (!response.ok) {
      const errorTimestamp = new Date().toISOString();
      const totalDuration = Date.now() - publishStartTime;

      console.error(`❌ [${errorTimestamp}] Facebook publishing failed after ${totalDuration}ms:`, {
        status: response.status,
        statusText: response.statusText,
        errorMessage: result.error?.message,
        errorType: result.error?.type,
        errorCode: result.error?.code,
        errorSubcode: result.error?.error_subcode,
        fbtrace_id: result.error?.fbtrace_id,
        fullError: result.error
      });

      // Provide specific Arabic error messages based on error type
      let arabicError = 'فشل في النشر على فيسبوك';
      if (result.error?.code === 190) {
        arabicError = 'انتهت صلاحية رمز الوصول - يرجى إعادة ربط الحساب';
      } else if (result.error?.code === 200) {
        arabicError = 'ليس لديك صلاحية للنشر على هذه الصفحة';
      } else if (result.error?.type === 'OAuthException') {
        arabicError = 'خطأ في المصادقة - يرجى إعادة ربط حساب فيسبوك';
      } else if (result.error?.message) {
        arabicError = `خطأ فيسبوك: ${result.error.message}`;
      }

      return {
        success: false,
        error: arabicError,
        details: {
          ...result,
          debug_info: {
            duration_ms: totalDuration,
            api_call_duration_ms: apiCallDuration,
            token_was_refreshed: tokenResult.refreshed,
            target_type: account.page_id ? 'PAGE' : 'USER',
            target_id: targetId
          }
        },
        step: 'post_publishing',
        platform: 'FACEBOOK'
      };
    }

    const successTimestamp = new Date().toISOString();
    const totalDuration = Date.now() - publishStartTime;

    console.log(`✅ [${successTimestamp}] Facebook post published successfully after ${totalDuration}ms:`, {
      postId: result.id,
      postUrl: `https://facebook.com/${result.id}`,
      duration_ms: totalDuration,
      api_call_duration_ms: apiCallDuration,
      token_refreshed: tokenResult.refreshed,
      target_type: account.page_id ? 'PAGE' : 'USER',
      target_id: targetId
    });

    return {
      success: true,
      postId: result.id,
      url: `https://facebook.com/${result.id}`,
      details: {
        ...result,
        debug_info: {
          duration_ms: totalDuration,
          api_call_duration_ms: apiCallDuration,
          token_was_refreshed: tokenResult.refreshed,
          target_type: account.page_id ? 'PAGE' : 'USER',
          target_id: targetId,
          published_at: successTimestamp
        }
      },
      platform: 'FACEBOOK'
    };

  } catch (error: any) {
    const errorTimestamp = new Date().toISOString();
    console.error(`❌ [${errorTimestamp}] Unexpected Facebook publishing error:`, {
      message: error.message,
      name: error.name,
      stack: error.stack,
      account_id: account?.account_id,
      account_name: account?.account_name,
      content_length: content?.length,
      has_media: !!(mediaUrls && mediaUrls.length > 0)
    });

    // Provide user-friendly Arabic error messages
    let arabicError = 'خطأ غير متوقع في النشر على فيسبوك';
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      arabicError = 'خطأ في الاتصال بخوادم فيسبوك - يرجى المحاولة مرة أخرى';
    } else if (error.message.includes('timeout')) {
      arabicError = 'انتهت مهلة الاتصال بفيسبوك - يرجى المحاولة مرة أخرى';
    } else if (error.message) {
      arabicError = `خطأ تقني: ${error.message}`;
    }

    return {
      success: false,
      error: arabicError,
      details: {
        error_type: error.name,
        error_message: error.message,
        stack_trace: error.stack,
        timestamp: errorTimestamp,
        debug_info: {
          account_id: account?.account_id,
          account_name: account?.account_name,
          content_length: content?.length,
          has_media: !!(mediaUrls && mediaUrls.length > 0)
        }
      },
      step: 'unexpected_error',
      platform: 'FACEBOOK'
    };
  }
}

/**
 * Enhanced Instagram Publisher with detailed error handling
 * FIXED: Updated to match working function signature pattern
 */
export async function publishToInstagramEnhanced(
  accessToken: string,
  instagramAccountId: string,
  content: string,
  mediaUrls?: string[]
): Promise<EnhancedPublishResult> {
  try {
    console.log('📸 [ENHANCED] FIXED - Function called with extracted parameters:', {
      accessToken_exists: !!accessToken,
      accessToken_type: typeof accessToken,
      accessToken_length: accessToken?.length,
      accessToken_preview: accessToken ? accessToken.substring(0, 20) + '...' : 'NULL/UNDEFINED',
      instagramAccountId,
      instagramAccountId_type: typeof instagramAccountId,
      content_length: content?.length,
      content_preview: content?.substring(0, 50) + '...',
      mediaUrls_count: mediaUrls?.length || 0,
      mediaUrls: mediaUrls
    });

    console.log('📸 [ENHANCED] FIXED - Publishing to Instagram with extracted parameters');

    // Step 1: Validate parameters (FIXED - no more account object dependency)
    console.log('🔍 CRITICAL DEBUG - Token validation check:', {
      accessToken_truthy: !!accessToken,
      accessToken_is_null: accessToken === null,
      accessToken_is_undefined: accessToken === undefined,
      accessToken_is_empty_string: accessToken === '',
      accessToken_actual_value: accessToken
    });

    if (!accessToken) {
      return {
        success: false,
        error: 'رمز الوصول لحساب انستغرام غير متوفر',
        details: 'Instagram access token not provided',
        step: 'token_validation',
        platform: 'INSTAGRAM'
      };
    }

    if (!instagramAccountId) {
      return {
        success: false,
        error: 'معرف حساب انستغرام التجاري غير متوفر',
        details: 'Instagram Business Account ID not provided',
        step: 'account_id_validation',
        platform: 'INSTAGRAM'
      };
    }

    // Step 3: Validate token using Facebook Graph API (FIXED - use provided token)
    console.log('🔑 Validating Instagram token...');
    const tokenResponse = await fetch(`https://graph.facebook.com/v19.0/me?access_token=${accessToken}`);
    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      console.error('❌ Instagram token validation failed:', tokenData);
      return {
        success: false,
        error: 'انتهت صلاحية رمز الوصول لحساب انستغرام. يرجى إعادة ربط الحساب.',
        details: tokenData,
        step: 'token_validation',
        platform: 'INSTAGRAM'
      };
    }

    console.log('✅ Instagram token is valid for user:', tokenData.name);

    // Step 4: Check media requirement
    if (!mediaUrls || mediaUrls.length === 0) {
      return {
        success: false,
        error: 'منشورات انستغرام تتطلب صورة أو فيديو واحد على الأقل',
        details: 'Instagram posts require at least one image or video',
        step: 'media_validation',
        platform: 'INSTAGRAM'
      };
    }

    const mediaUrl = mediaUrls[0];
    console.log(`📸 Publishing to Instagram Business Account: ${instagramAccountId}`);

    // Step 5: Create media container
    console.log('📦 Creating Instagram media container...');

    // Facebook Graph API requires form-encoded data, not JSON
    const containerFormData = new URLSearchParams();
    containerFormData.append('image_url', mediaUrl);
    containerFormData.append('caption', content);
    containerFormData.append('access_token', accessToken);

    console.log('🔍 DEBUGGING - Instagram container request:', {
      url: `https://graph.facebook.com/v19.0/${instagramAccountId}/media`,
      instagramAccountId,
      mediaUrl,
      caption: content.substring(0, 50) + '...'
    });

    const containerResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramAccountId}/media`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: containerFormData,
      }
    );

    const containerData = await containerResponse.json();
    console.log('🔍 DEBUGGING - Instagram container response:', containerData);

    if (!containerResponse.ok) {
      console.error('❌ Instagram container creation failed:', containerData);
      return {
        success: false,
        error: containerData.error?.message || 'فشل في إنشاء حاوية الوسائط لانستغرام',
        details: containerData,
        step: 'container_creation',
        platform: 'INSTAGRAM'
      };
    }

    const containerId = containerData.id;
    console.log('✅ Instagram media container created:', containerId);

    // Step 6: Publish the media container
    console.log('📤 Publishing Instagram media container...');

    // Facebook Graph API requires form-encoded data, not JSON
    const publishFormData = new URLSearchParams();
    publishFormData.append('creation_id', containerId);
    publishFormData.append('access_token', accessToken);

    console.log('🔍 DEBUGGING - Instagram publish request:', {
      url: `https://graph.facebook.com/v19.0/${instagramAccountId}/media_publish`,
      containerId
    });

    const publishResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramAccountId}/media_publish`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: publishFormData,
      }
    );

    const publishData = await publishResponse.json();
    console.log('🔍 DEBUGGING - Instagram publish response:', publishData);

    if (!publishResponse.ok) {
      console.error('❌ Instagram publish failed:', publishData);
      return {
        success: false,
        error: publishData.error?.message || 'فشل في نشر المنشور على انستغرام',
        details: publishData,
        step: 'media_publish',
        platform: 'INSTAGRAM'
      };
    }

    const postId = publishData.id;
    console.log('✅ Instagram post published successfully:', postId);

    return {
      success: true,
      postId: postId,
      url: `https://instagram.com/p/${postId}`,
      details: publishData,
      platform: 'INSTAGRAM'
    };

  } catch (error: any) {
    console.error('❌ Instagram publishing error:', error);
    return {
      success: false,
      error: error.message || 'خطأ غير متوقع في النشر على انستغرام',
      details: error.stack,
      step: 'unexpected_error',
      platform: 'INSTAGRAM'
    };
  }
}
